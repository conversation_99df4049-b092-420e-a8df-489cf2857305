@import url("https://fonts.googleapis.com/css2?family=Montserrat&display=swap");
@import "./variables.css";

html,
body {
  scroll-behavior: smooth;
  overflow-anchor: auto;
}

.eighty-width {
  width: 80%;
}

.reflect {
  transform: scaleX(-1);
}

.mermaid-syntax {
  position: absolute;
  left: 11px;
}

.bg-gradient-gradient_dark {
  background: repeating-radial-gradient(#060018, #0a001c 100px);
}

.menu-list {
  width: var(--sidebar-menus-width);
  list-style: none !important;
  margin: auto;
  margin-inline-start: inherit !important;

  a {
    background-color: transparent;
    color: #000;
  }

  li[data-thread-id]:hover {
    border-color: hsl(0deg 0% 37.2%);

    .chat-list-item-menu-btn {
      display: none;
    }
  }

  li[data-thread-id] {
    border: 1px solid var(--border-color);
    border-radius: var(--bulma-control-radius);
    position: relative;
    margin: 7px auto;

    &.active-chat {
      border: 1px solid var(--hightlight-orange);
    }

    .chat-list-item-menu-btn {
      opacity: 0.3;
      font-weight: bold;
      position: absolute;
      right: 0px;
      height: 9px;
      background: #f5f5dc4f;
      line-height: 0.00001;
      padding: 0 3px 0 4px;
      border-top-right-radius: var(--bulma-control-radius);
      border-bottom-left-radius: var(--bulma-control-radius);
    }
  }

  .editable-chat-item {
    display: flex;
    align-items: center;
  }

  .chat-item-actions {
    display: none;
    margin-left: 8px;

    .button {
      height: 27px !important;
      width: 27px;
      display: flex;
      border: 1px solid #a9a9a924;
      background: none;
      cursor: pointer;
      text-decoration: none;
    }
    .button:hover {
      background-color: var(--divi-purp) !important;
    }
  }

  .editable-chat-item:hover .chat-item-actions {
    display: inline-flex;
    position: absolute;
    right: 0;
  }

  input {
    width: 100%;
    padding: 3px;
    background: #333;
    color: #fff;
    border: 2px solid #555;
    border-radius: 3px;
  }
}

.mermaid-container {
  position: relative;
  width: 100%;
  display: initial;
  padding: 200px;
  text-align: center;
}

.download-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background: transparent;
  border: none;
  font-size: 24px;
  cursor: pointer;
  visibility: hidden;
}

.mermaid-container:hover .download-button {
  visibility: visible;
}

.EmojiPickerReact {
  --epr-emoji-size: 22px;
  --epr-emoji-gap: 22px;
}

.feedback-input-container .feedback-textarea {
  width: 100%;
  height: 100px;
  /* Adjust based on preference */
  margin-top: 8px;
  padding: 8px;
  box-sizing: border-box;
  border-radius: 4px;
  border: 1px solid #ccc;
  /* Example styling */
}

.feedback-textarea {
  width: 95%;
  border-radius: 7px;
  border: 2px solid orange;
}
.message-feedback-submit {
  display: block;
  float: right;
  margin: 5px 3px 9px;
}

a {
  color: var(--divi-dark-purp);
}

html,
body,
ul {
  font-family: "Montserrat", sans-serif;
  margin: 0;
  padding: 0;
  font-size: 14px;
}

.main-header {
  background: linear-gradient(
    90deg,
    var(--main-header-gradient-start) 0%,
    var(--main-header-gradient-mid) 35%,
    var(--main-header-gradient-end) 100%
  );
  box-shadow:
    0 0.5em 1em -0.125em rgba(10, 10, 10, 0.1),
    0 0px 0 1px rgba(10, 10, 10, 0.02);
  padding: 7px;
  display: flex;
  align-items: center;
  min-height: 73px;

  img.is-rounded.chat-avatar.chat-avatar-bg {
    width: 47px !important;
    height: 47px !important;
    max-height: 47px;
    max-width: 47px;
    border: 2px solid #000;
    vertical-align: text-top;
    cursor: pointer;
  }
  .navbar-brand {
    width: var(--sidebar-open-width);
  }

  .navbar-link {
    border-radius: 5px;
  }

  .navbar-user-picture {
    margin: auto 15px;

    .dropdown-menu {
      left: -42px;
      width: 25px;
    }

    .dropdown-content {
      background-color: var(--button-bg-color);
      border-radius: 3px;
    }
    .dropdown-item {
      padding: 11px 33px;
    }
  }

  .title {
    margin-left: 71px;
    color: var(--divi-dark-purp);
  }
  .topMenu-wrapper {
    position: absolute;
    right: 0;
  }
  .topMenu,
  .bottomMenu {
    font-size: 15px;
  }

  .divinci-header-icon {
    position: absolute;
    height: 97px;
    border-radius: 50%;
    z-index: 1;
    width: 91px;
    top: -4px;
    left: -2px;
    background: rgb(245, 243, 255);
    background: radial-gradient(
      circle,
      rgba(245, 243, 255, 1) 0%,
      rgba(247, 246, 255, 1) 100%
    );

    .divi-header-icon {
      filter: grayscale(1) invert(1);
      height: 53%;
      max-height: revert;
      margin: auto;
      border-radius: 11px;
    }
  }

  .chat-title {
    flex: auto;
    flex-flow: wrap;
    margin: 0;
    justify-content: space-around;
    padding: 0 10px;
  }
  .chat-title-dropdown-content {
    min-width: 100%;
  }
}

.left-sidebar:hover .chat-list-item-menu-btn {
  opacity: 0.7;
}

.left-sidebar,
.right-sidebar {
  background: linear-gradient(
    90deg,
    var(--main-header-gradient-start) 0%,
    var(--main-header-gradient-mid) 35%,
    var(--main-header-gradient-end) 100%
  );
}

.left-sidebar {
  height: 100%;
  border-right: 1px solid var(--divi-purp);
  overflow-x: hidden;
  padding: 8px;
  width: var(--sidebar-width);

  .menu {
    margin: auto;
  }

  &:hover ~ .chat-list-item-menu-btn {
    opacity: 0.5;
  }

  h1,
  h2,
  h3,
  h4 {
    margin: auto;
    width: var(--sidebar-menus-width);
    background: var(--main-header-gradient-end);
  }
}

.left-sidebar.drawer-open {
  width: var(--sidebar-open-width);
}

.left-sidebar .create-chat-button {
  display: flex;
  width: var(--sidebar-menus-width);
  margin: 22px auto 0;
}

.left-sidebar .drawer-toggle-button {
  position: absolute;
  top: 48%;
  left: calc(var(--sidebar-width) - 42px);
  background: var(--divi-purp);
  background: var(--background-gradient-end);
  width: 16px;
  height: 60px;
  display: block;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
  cursor: pointer;
  border: 1px solid hsl(221, 14%, 29%);
  border-left: 0;
  z-index: 2;
}

.left-sidebar .menu-label {
  margin: 23px 0 11px 11px;
  font-size: 13px;
  color: #000;
}

.left-sidebar .dropdown-menu {
  min-width: max-content;
}

.left-sidebar .dropdown-menu .dropdown-content {
  padding: 0;
}

.mobile-menu-button {
  width: var(--mobile-menu-width);
  cursor: pointer;
  fill: var(--button-text-color);
  border: 2px solid grey;
  padding: 4px;
  border-radius: 4px;
  background-color: var(--button-bg-color);
  color: var(--button-text-color);
  box-shadow: 1px 1px #0e14547a;
}

.chat-message-content {
  max-height: 150px;
  overflow: hidden;
}

.chat-message-content.expanded {
  max-height: none;
}

.expand-button {
  margin-top: 8px;
}

.rating-icon {
  fill: #000;
  height: 14px;
  padding: 2px;
  opacity: 0.5;
}
.thumbs-down-icon {
  transform: rotate(180deg);
}

.transcript-users-container {
  display: flex;
  position: fixed;
  top: 73px;
  z-index: 2;
  right: 0px;
  padding: 6px 0 7px 9px;
  border-bottom-left-radius: 11px;
  visibility: hidden;
}

.transcript-users-img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 5px;
}

.user-avatar {
  width: var(--mobile-menu-width);
  height: var(--mobile-menu-width);
  margin: 0 11px;
}

.right-sidebar {
  display: none;
  transition: width 0.3s;
  width: var(--right-drawer-width);
  border-left: 1px solid var(--divi-purp);
}
.right-sidebar.drawer-open {
  display: block;
  padding: 20px;
  z-index: 0;
}

.toggle-right-drawer-button {
  transition: width 0.3s;
  transition: height 0.3s;
  position: fixed;
  right: 0;
  top: 48%;
  background: var(--divi-purp);
  background: var(--background-gradient-end);
  width: 16px;
  height: 60px;
  display: block;
  border-radius: 10px 0 0 10px;
  cursor: pointer;
  border: 1px solid hsl(221, 14%, 29%);
  border-right: 0;
  z-index: 0;
  padding: 0;
}

.right-drawer-open {
  margin-right: 300px;
  right: 0;
}

.dropdown-menu#dropdown-menu-right {
  position: fixed;
  right: 10px;
  /* Adjust as needed */
  top: 50%;
  transform: translateY(-50%);
}

.dropdown-content {
  box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
  background-color: #F3F3F7;
}

/* Adjustments for hover effect */
.dropdown-trigger:hover ~ .dropdown-menu,
.dropdown-menu:hover {
  /* display: block; */
  display: flex;
}

.dropdown-menu {
  display: none;
  /* Initially hidden */
}

.drawer {
  visibility: hidden;
  transform: translateY(-100%);
  z-index: 1;
  position: relative;

  &.is-active {
    visibility: visible;
    transform: translateY(48%);
  }

  .panel-block {
    position: absolute;
    bottom: -27px;
    width: var(--sidebar-menus-width);
  }
}

.text-to-speech-svg {
  width: 50px;
  height: 50px;
  overflow: visible;
  border-radius: 5px;
  padding: 5px;
  fill: #2b3055;
}

.button.is-outlined.send-chat-button {
  font-weight: bold;
  background-color: var(--divi-dark-purp);

  :disabled {
    /* Example styling for disabled state */
    background-color: #ccc;
    color: #999;
  }
}

.burger-menu-mini {
  margin: 0 5px 0 3px;
  vertical-align: middle;
  rotate: 180deg;
  cursor: pointer;
}

.chat-input-form {
  display: flex;
  flex-direction: row;
  width: 100%;
  border: 6px solid white;
  padding: 0;
  border-top-right-radius: 9px;
  border-top-left-radius: 9px;

  .textarea {
    background: radial-gradient(
      circle,
      rgba(249, 250, 255, 0.027070203081232536) 0%,
      rgba(255, 246, 248, 0.013064600840336116) 100%
    );

    &:focus {
      background: white;
    }

    &:not(:focus) {
      border-color: dodgerblue;
    }
  }
}

.chat-submit-buttons {
  background: white;
  justify-content: space-evenly;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 33px;
  z-index: 1;
  align-self: stretch;
}

.chat-input-menu {
  display: flex;
  position: absolute;
  bottom: 0px;
  right: 3px;
  z-index: 3;

  .suggested-category-btn {
    display: flex;
    /* align-self: self-end; */
    align-self: center;
    margin-right: 8px;
    padding-bottom: 6px 12px;
  }

  .chat-inline-settings {
    position: relative;
    padding: 0 0 0 13px;

    .panel-block {
      border: 1px solid #dbdbdb;
      background: white;
      padding: 0 13px;
    }

    &.panel {
      padding: 13px;
      box-shadow: none;
    }
  }
}

.divinci-chat-category-toggle {
  padding: 5px 7px;
  display: flex;
  align-items: center;
}

.form-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  background: #fff;
  z-index: 2;
  padding: 10px 0 40px 20px;
}

.chat-options-toggle-button {
  align-self: center;
  padding: 5px 5px 8px 5px;
  height: 33px;
  color: #4f558c;
  font-size: 33px;
  margin: 0;
  cursor: pointer;

  &.is-drawer-open {
    font-size: 21px;
    border: 0;
    padding: 5px;
  }
}

.textarea {
  min-width: auto;
}

.is-underlined {
  text-decoration: underline;
}

.speech-button {
  height: auto;
  padding: 2px;
}

.chat-inline-settings.panel:not(:last-child) {
  margin-top: -81px;
}

.chat-inline-settings.drawer.panel div {
  background: var(--white-black);
  border-top-left-radius: 11px;
  border-top-right-radius: 11px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  margin: 7px;
  color: #4f558c;
}

.emoji-notification {
  position: absolute;
  bottom: 35px;
  width: 100%;
}

.reply-info-tag {
  display: flex;

  .reply-info {
    opacity: 0.3;
    font-size: 12px;
    padding: 7px;
  }
}

.notification-settings-form {
  margin-bottom: 75px;
  border-bottom: 1px solid grey;
  padding-bottom: 55px;
}

.default-notification-settings {
  margin: 75px 0;
}

@media only screen and (min-width: 500px) and (min-height: 500px) {
  .topMenu li {
    margin: auto 1px;
    padding: 1px;
  }

  .transcript-users-container {
    display: none;
  }

  .user-picture {
    width: 96px;
    height: 96px;
  }

  .user-card {
    width: 222px;

    .title,
    .subtitle {
      color: #000;
    }
  }
}

.mobile-header {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  min-height: 100vh; /* Ensure minimum height */

  .mobile-menu-li {
    padding: 9px;
    margin: 22px 33px;
    font-size: 22px;
    background: dimgray;
    color: #000;
    a {
      color: #000;
    }
  }

  .main-header {
    display: flex;
    flex-direction: row;
    width: 100%;
    z-index: 3;
    flex-shrink: 0; /* Prevent header from shrinking */
  }

  /* Main content area should grow to fill available space */
  main.content {
    flex: 1 1 auto;
    overflow-y: auto;
  }

  /* Mobile menu aside */
  aside.menu {
    flex: 1 1 auto;
    overflow-y: auto;
  }

  /* Footer should stick to bottom */
  footer {
    flex-shrink: 0;
    margin-top: auto;
  }
}

@media only screen and (max-width: 600px), only screen and (max-height: 600px) {
  .chat-submit-buttons {
    align-items: start;
    padding: 0 15px;
  }
}

.chat-avatar-bg {
  background: var(--divi-dark-purp);
  background: radial-gradient(
    circle,
    var(--button-bg-color) 16%,
    var(--divi-dark-purp) 100%
  );
}

.is-rounded {
  border-radius: 50%;
}

.chat-transcript {
  display: flex;
  flex-direction: column;
  padding: 33px 0;

  .chat-message-assistant {
    justify-content: start;

    .card {
      color: #201b56;
    }
  }
  .message-id-card,
  .message-timestamp {
    color: var(--divi-dark-purp);
  }
  .message-id-card:hover,
  .message-timestamp:hover {
    color: hsl(0deg 0% 71%);
  }

  .chat-message {
    width: 90%;
    border-radius: 5px;
    margin: 4px;
    box-sizing: content-box;
    padding: 17px;
    list-style: none;
    display: flex;

    & > * > * {
      padding: 4px 0px;
    }

    &.user {
      align-self: flex-start;
    }

    .user-title {
      padding-right: 39px;
    }

    &.assistant,
    &.error {
      align-self: flex-end;
    }

    .card-header {
      background: rgb(241, 240, 245);
      background: linear-gradient(
        0deg,
        rgba(241, 240, 245, 1) 0%,
        rgba(251, 251, 252, 1) 100%
      );
      border-top-left-radius: 15px;
      border-top-right-radius: 15px;

      .emoji-picker-container {
        position: absolute;
        top: 0;
        right: 0;
        padding: 0;

        .EmojiPickerReact {
          z-index: 4;
        }
      }
    }

    .card-footer {
      background: rgb(241, 240, 245);
      background: linear-gradient(
        180deg,
        rgba(241, 240, 245, 1) 0%,
        rgba(251, 251, 252, 1) 100%
      );
      border-bottom-left-radius: 15px;
      border-bottom-right-radius: 15px;
    }

    .card-footer-left {
      align-self: "center";
      display: "flex";
      flex-direction: "column";
      align-items: "flex-end";
      position: relative;

      .chat-avatar {
        position: absolute;
        bottom: -14px;
        right: -12px;
        cursor: pointer;
      }
    }

    .card {
      min-width: 300px;
      background-color: transparent;
    }

    .card > * > * {
      padding: 3px 7px;
    }

    .reply {
      cursor: pointer;
    }
  }
}

.main {
  flex-grow: 1;
  overflow: auto;
}

.mainInner {
  height: 100%;
  display: flex;
  background: #f1f1f1;

  .chat-item-wrapper {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;

    .scroll-to-bottom-button {
      color: black;
      font-size: 22px;
      position: fixed;
      bottom: 135px;
      right: 0;
      z-index: 2;
    }
  }
}

.chat-message-container {
  display: flex;
  align-items: flex-start;
  /* Adjust based on desired vertical alignment */
  gap: 10px;
  /* Spacing between message card and timestamp */
}

.edited-message {
  border: 3px solid #ffa5008c;
}

.message-timestamp-side {
  margin: 70px 0 0 0;
}

.chat-transcript-area {
  flex-grow: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column-reverse;
  padding-bottom: 33px;
  background: linear-gradient(
    90deg,
    var(--chat-transcript-bg-start) 0%,
    var(--chat-transcript-bg-end) 100%
  );
  position: relative;
  max-height: calc(100vh - 100px);

  .card-content ol,
  .card-content ul {
    padding: 12px 24px !important;
  }

  .card-content p {
    font-size: 15px;
    line-height: 26px;
    padding: 13px;
  }

  .date-divider {
    justify-content: center;
    width: 100%;
    display: flex;
    padding: 11px 0;
  }

  .card-header {
    display: flex;
    flex-direction: row;

    .message-timestamp {
      align-self: "center";
      display: "flex";
      flex-direction: "column";
      align-items: "flex-end";
    }

    .card-header-column {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
    }
  }

  .chat-message-user {
    justify-content: end;

    .card-header,
    .card-footer,
    .card-content {
      color: white;
      width: 100%;
      background-color: #0085ea;
      background: linear-gradient(
        0deg,
        rgba(0, 133, 234, 1) 0%,
        rgba(9, 123, 210, 1) 100%
      );
    }

    .card-content {
      background-color: #097bd2;
    }

    .card-footer {
      border-top: 1px #107acb solid;
      background-color: #0085ea;
      background: linear-gradient(
        180deg,
        rgba(0, 133, 234, 1) 0%,
        rgba(9, 123, 210, 1) 100%
      );
    }
  }
}

footer {
  .copyright {
    padding-left: 11px;
  }
}

.emoji-counts-wrapper {
  width: 100%;
  justify-content: start;
  display: flex;
}
.emoji-counts {
  display: flex;
  margin: 0 27px -22px -25px;
  background-color: aliceblue;
  border-radius: 13px;
  padding: 0 !important;

  .emoji-bubble {
    display: inline-flex;
    align-items: center;
    background-color: #f0f0f0;
    border-radius: 16px;
    padding: 4px 8px;
    font-size: 0.9em;
    border: 1px solid aliceblue;
    cursor: pointer;

    .icon {
      display: contents;
    }
  }

  .emoji-bubble .emoji-icon {
    margin-right: 4px;
    display: inline-flex;
    /* Space between emoji and count */
  }

  .emoji-tooltip {
    position: relative;
    display: inline-block;
  }

  .emoji-tooltip .tooltip-text {
    visibility: hidden;
    font-size: 11px;
    background-color: var(--divi-dark-purp);
    color: rgb(246, 246, 246);
    text-align: center;
    border-radius: 6px;
    padding: 5px 11px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
  }

  .emoji-tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: black transparent transparent transparent;
  }

  .emoji-tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
  }
}

.whitelabel {
  .whitelabel-ai-card {
    width: 333px;
    padding: 33px;
  }

  .create-whitelabel-button {
    margin: 22px 0 0;
  }

  .tile.is-outlined {
    border-radius: 15px;
    border: 1px solid lightgray;
    padding: 22px;
  }

  .file-name-list {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px;
    margin-top: 5px;
  }

  .file-name {
    padding: 2px 5px;
    margin-bottom: 2px;
    border-radius: 2px;
    background-color: #f3f3f3;
  }

  .file {
    display: initial;
  }
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  body {
    background-color: var(--background-gradient-start);
    color: var(--text-color);
  }

  .chat-inline-settings.drawer.panel div {
    background: var(--white-black);
    border-top-left-radius: 11px;
    border-top-right-radius: 11px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    margin: 7px;
  }
  a,
  .title {
    color: #ccc !important;
  }

  .divinci-header-icon img {
    filter: none !important;
  }

  .divinci-header-icon,
  .form-container,
  .chat-input-form .textarea,
  .chat-submit-buttons {
    background: var(--background-gradient-start) !important;
    color: #fff;
  }

  .chat-input-form,
  .card-footer {
    border: var(--divi-dark-purp) !important;
  }

  .mainInner {
    background-color: var(--background-gradient-start) !important;

    .chat-item-wrapper {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;

      .scroll-to-bottom-button {
        color: var(--divi-light-purp);
        font-size: 22px;
        position: fixed;
        bottom: 133px;
        right: 0;
        z-index: 1;
      }
    }
  }

  .main-header {
    & .navbar-user-picture {
      .dropdown-content {
        background-color: #000;
        border-radius: 3px;
      }
    }
  }

  .transcript-users-container {
    background-color: var(--background-gradient-start) !important;
  }

  .button:not(.is-link):not(.is-ghost) {
    background-color: var(--background-gradient-end) !important;
    color: #ccc !important;
  }

  li.chat-message.chat-message-user button {
    color: #fff !important;
  }

  .chat-card,
  .card-header {
    box-shadow: none;
  }

  .chat-transcript .chat-message {
    .emoji-bubble,
    .emoji-counts {
      color: #ccc;
      background: #333;
    }

    .emoji-bubble {
      background: #111;
      border: 1px solid #222;
    }

    .rating-icon {
      fill: white;
      height: 14px;
      padding: 2px;
      opacity: 0.5;
    }

    .chat-avatar-bg {
      background: var(--divi-dark-purp);
      background: radial-gradient(
        circle,
        rgba(51, 44, 87, 1) 16%,
        var(--divi-dark-purp) 100%
      );
    }
  }

  .chat-transcript .chat-message.chat-message-assistant .card {
    .card-content p strong {
      filter: invert(0);
    }
  }

  .chat-transcript .chat-message.chat-message-assistant .card {
    color: #ccc;

    .card-header,
    .card-footer {
      color: #ccc;
      background: #000 !important;
    }
  }

  .transcript-users-container {
    background-color: var(--background-gradient-start) !important;
  }

  .button:not(.is-link):not(.is-ghost) {
    background-color: var(--background-gradient-end) !important;
    color: #ccc;
  }

  li.chat-message.chat-message-user button {
    color: #fff !important;
  }

  .chat-card,
  .card-header {
    box-shadow: none;
  }

  .chat-transcript .chat-message {
    .emoji-bubble,
    .emoji-counts {
      color: #ccc;
      background: #333;
    }
  }

  .emoji-bubble {
    background: #111;
    border: 1px solid #222;
  }
}

.chat-transcript .chat-message.chat-message-assistant .card {
  color: var(--divi-dark-purp);

  strong {
    color: var(--divi-dark-purp);
  }

  .card-header,
  .card-footer {
    background: var(--response-chat-card-bg);
    border: var(--response-chat-card-bg);
  }

  .card-content {
    background: var(--response-chat-card-bg);
  }

  .text-to-speech-svg {
    fill: #d0d2e5;
  }
}

.form-error {
  color: #f00;
}

.error-message {
  padding: 7px;
  border-radius: 5px;
  color: #f00;
}

ul.block-list > li {
  padding: 5px;
  border-radius: 5px;
  margin: 5px;
  border: #000 solid 1px;
}

.progress-fade-in {
  transition: opacity 0.5s;
  opacity: 1;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 100;
  }
}

.progress-fade-out {
  transition: opacity 0.5s;
  opacity: 0;
}

@keyframes fade-out {
  from {
    opacity: 100;
  }
  to {
    opacity: 0;
  }
}

@media (max-width: 768px) {
  .chat-transcript-area {
    padding-bottom: 140px; /* Add extra padding at the bottom to prevent content from being hidden behind the fixed input form */
    max-height: calc(100vh - 180px); /* Adjust max height to account for the fixed input form */
  }

  .scroll-to-bottom-button {
    bottom: 170px !important; /* Position above the fixed chat input form */
    right: 10px !important;
    z-index: 9 !important;
  }
  .left-sidebar,
  .right-sidebar {
    width: var(--sidebar-width);
    position: fixed;
    left: -1;
    top: 73px;
    height: 100vh;
    z-index: 2;
  }

  .left-sidebar.drawer-open {
    width: var(--sidebar-open-width);
  }

  .left-sidebar .drawer-toggle-button {
    left: calc(var(--sidebar-width) - 42px);
    position: inherit;
  }

  .left-sidebar.drawer-open .drawer-toggle-button {
    left: var(--sidebar-open-width);
  }

  .right-sidebar {
    left: auto;
    right: 0;
    padding: 2px;
  }
  .right-sidebar.drawer-open {
    padding: 20px;
  }

  /* Right drawer toggle button positioning for mobile */
  .toggle-right-drawer-button {
    position: fixed !important;
    right: 0 !important;
    top: 48% !important;
    z-index: 3 !important;
  }

  /* When right drawer is open in mobile, position button to the left of the drawer */
  .toggle-right-drawer-button.right-drawer-open {
    right: var(--right-drawer-width, 300px) !important;
  }

  .drawer-toggle-button.dropdown.is-hoverable
    .dropdown-menu.drawer-dropdown-menu {
    display: none;
  }

  .container.is-fluid {
    padding: 0;
  }

  .container {
    padding-left: 10px;
    padding-right: 10px;
  }

  .content ul {
    margin-inline-start: 0;
  }
  li {
    list-style: none;
  }

  .box, .container, .section, .container.is-fluid {
    padding: 2px;
    margin: 2px;
  }

  body,
  p,
  .menu-list,
  .chat-message {
    font-size: 16px; /* Adjust for tablets */
  }

  .navbar-item {
    justify-content: end;
  }

  .mobile-menu-button {
    width: var(--mobile-menu-width, 40px);
    height: 40px; /* Set a fixed height */
    flex-shrink: 0; /* Prevent shrinking */
    cursor: pointer;
    fill: var(--button-text-color);
    border: 2px solid grey;
    padding: 4px;
    border-radius: 4px;
    background-color: var(--button-bg-color);
    color: var(--button-text-color);
    box-shadow: 1px 1px #0e14547a;
    margin-left: 11px;
  }

  .chat-input-form {
    margin-bottom: 0;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 10;
    background-color: #f9f9f9;
    border-top: 1px solid #ddd;
    padding-top: 10px;
  }

  .chat-submit-buttons {
    display: flex;
    align-items: center;
    padding: 0 33px 35px 33px;
    gap: 10px; /* Add consistent spacing between buttons */
  }

  .chat-submit-buttons .chat-input-menu {
    margin-left: auto; /* Push the gear icon to the right */
  }

  .chat-options-toggle-button {
    height: 38px; /* Match the height of other buttons */
    width: 38px;
    margin-left: 10px; /* Add some spacing */
    order: 2; /* Place after other buttons */
  }

  .chat-submit-buttons {
    padding: 0 71px 35px 15px;
    display: flex;
    align-items: center;
    position: relative;
  }

  .chat-submit-buttons .chat-input-menu {
    order: 2;
  }

  .suggested-category-btn {
    display: none !important;
  }

  .chat-input-menu {
    position: fixed;
    bottom: auto;
    padding: 3px 5px 0 0;
  }

  .button.is-outlined.speech-button {
    border-color: hsl(var(--bulma-button-h), var(--bulma-button-s), calc(var(--bulma-button-border-l) + var(--bulma-button-border-l-delta)));
  }

  /* Mobile footer positioning */
  .mobile-header footer {
    position: relative;
    bottom: 0;
    width: 100%;
    z-index: 6;
    background: var(--main-header-gradient-mid);
  }


}

@media (max-width: 480px) {
  body,
  p,
  .menu-list,
  .chat-message {
    font-size: 14px; /* Adjust for smaller screens */
  }

  .menu-list li {
    font-size: 12px;
  }

  .main-header .title {
    font-size: 18px;
  }

  .chat-message-content {
    font-size: 13px;
  }
}

.submenu-list {
  margin-left: 1rem !important;
  list-style: none;
}

.submenu-item {
  padding: 0.5rem 1rem;
  display: block;
  color: inherit;
}

.mobile-menu-li {
  padding: 0.5rem 0;
}

@media (max-width: 768px) {
  .submenu-list {
    background-color: rgba(0, 0, 0, 0.02);
    border-left: 2px solid #dbdbdb;
  }
}
