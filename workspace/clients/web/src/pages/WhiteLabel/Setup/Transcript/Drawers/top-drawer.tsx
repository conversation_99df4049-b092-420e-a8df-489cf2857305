import React, { useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import { showNotificationTemporarily } from "../../Notifications/notificationUtils";
import { usePrefixManagement } from "./hooks/usePrefixManagement";
import {
  NotificationBanner,
  PrefixEditor,
  DrawerToggle
} from "./components";

import {
  LOCAL_STORAGE_KEYS, TOP_SIDEBAR_WIDTH_OPEN, ZERO_PX,
} from "../../../../../globals/constants/values";

import "./top-drawer.css";

interface TopDrawerProps {
  drawerOpen: boolean,
  toggleDrawer: (
    isOpen: boolean,
    localStorageKey: string,
    cssVarOpenWidth: string,
    cssVarClosedWidth: string,
  ) => void,
}

export const TopDrawer: React.FC<TopDrawerProps> = ({ drawerOpen, toggleDrawer })=>{
  const [showNotification, setShowNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768); // Track screen width
  const topDrawerRef = useRef<HTMLDivElement>(null);
  const { whitelabelId } = useParams();

  const {
    prefix,
    setPrefix,
    fetchExistingPrefix,
    savePrefix
  } = usePrefixManagement(whitelabelId || "");

  useEffect(()=>{
    if(whitelabelId) {
      fetchExistingPrefix();
    }
  }, [whitelabelId, fetchExistingPrefix]);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const handleSave = async ()=>{
    const result = await savePrefix();
    const message = result.success
      ? `🟢 Prefix ${result.isUpdate ? "updated" : "created"} successfully!`
      : `❌ Error ${result.isUpdate ? "updating" : "creating"} Pinned Prefix.`;

    showNotificationTemporarily(setShowNotification, setNotificationMessage, message);
  };

  const handleToggleDrawer = ()=>{
    toggleDrawer(
      drawerOpen,
      LOCAL_STORAGE_KEYS.TOP_DRAWER_OPEN,
      "-34px",
      ZERO_PX,
    );
  };

  return (
    <div
      ref={topDrawerRef}
      className={`top-drawer ${drawerOpen ? "drawer-open" : ""}`}
      style={{
        height: drawerOpen ? TOP_SIDEBAR_WIDTH_OPEN : ZERO_PX,
        padding: drawerOpen
          ? isMobile
            ? "10px 10px"
            : "10px 100px"
          : ZERO_PX,
      }}
    >
      {drawerOpen && (
        <div className="top-drawer-content">
          <NotificationBanner
            show={showNotification}
            message={notificationMessage}
          />
          <div className="chat-message-content">
            <div className="message-prefix-icon">📍</div>
            <div className="card chat-card edited-message">
              <div className="card-header" />
              <PrefixEditor
                prefix={prefix}
                onPrefixChange={setPrefix}
                onSave={handleSave}
                isVisible={drawerOpen}
              />
            </div>
          </div>
        </div>
      )}
      <DrawerToggle
        isOpen={drawerOpen}
        onToggle={handleToggleDrawer}
      />
    </div>
  );
};
