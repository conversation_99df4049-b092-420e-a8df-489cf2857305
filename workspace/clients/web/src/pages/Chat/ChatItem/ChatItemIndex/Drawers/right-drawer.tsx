import React, { useEffect, useRef } from 'react';
import {
  LOCAL_STORAGE_KEYS, RIGHT_SIDEBAR_WIDTH_OPEN, ZERO_PX,
} from "../../../../../globals/constants/values";

interface RightDrawerProps {
  rightDrawerOpen: boolean;
  toggleDrawer: (
    isOpen: boolean,
    localStorageKey: string,
    cssVarOpenWidth: string,
    cssVarClosedWidth: string,
  ) => void;
}

export const RightDrawer = ({
  rightDrawerOpen,
  toggleDrawer,
}: RightDrawerProps) => {
  const sidebarRef = useRef<HTMLDivElement>(null);
  const toggleButtonRef = useRef<HTMLDivElement>(null);

  const updateToggleButtonPosition = () => {
    if(sidebarRef.current && toggleButtonRef.current) {
      // Check if we're in mobile view (768px or less)
      const isMobile = window.innerWidth <= 768;

      if (isMobile) {
        // In mobile view, let CSS handle positioning with !important rules
        // Remove any inline styles that might conflict
        toggleButtonRef.current.style.right = '';
      } else {
        // In desktop view, use dynamic positioning
        toggleButtonRef.current.style.right = `${sidebarRef.current.offsetWidth}px`;
      }
    }
  };

  useEffect(() => {
    const handleResize = () => {
      updateToggleButtonPosition();
    };

    window.addEventListener("resize", handleResize);
    updateToggleButtonPosition(); // Initial update

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  useEffect(() => {
    updateToggleButtonPosition();
  }, [rightDrawerOpen]);

  return (
    <>
      <div
        ref={sidebarRef}
        className={`right-sidebar ${rightDrawerOpen ? "drawer-open" : ""}`}
        style={{
          width: `${rightDrawerOpen ? RIGHT_SIDEBAR_WIDTH_OPEN : ZERO_PX}`, // Default width values for open and closed states
        }}
      >
        <button
          disabled
          className="button create-chat-button"
          onClick={() => {/* Logic to add new AI */}}
        >Add New AI</button>
        <button
          disabled
          className="button assign-roles-button"
          onClick={() => {/* Logic to assign roles/instructions */}}
        >Assign Roles</button>
        <input disabled type="range" onChange={() => {/* Logic to modify temperature */}} />
      </div>
      <div className="dropdown-trigger" ref={toggleButtonRef}>
        <button
          className={`toggle-right-drawer-button ${rightDrawerOpen ? 'right-drawer-open' : ''}`}
          aria-expanded={rightDrawerOpen}
          aria-controls="right-drawer"
          onClick={() => toggleDrawer(
            rightDrawerOpen,
            LOCAL_STORAGE_KEYS.RIGHT_DRAWER_OPEN,
            RIGHT_SIDEBAR_WIDTH_OPEN, ZERO_PX,
          )}
        >
        </button>
      </div>
      <div className="dropdown-menu" id="dropdown-menu4" role="menu">
        <div className="dropdown-content">
          <div className="dropdown-item">
            <p>Close sidebar</p>
          </div>
        </div>
      </div>
    </>
  );
};
